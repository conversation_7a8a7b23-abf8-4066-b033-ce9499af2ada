"use client"

import { useState, useEffect, use<PERSON>allback, useRef, useMemo } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import Image from "next/image"
import { ThumbsUp, MessageSquare, ChevronLeft, ChevronRight, Layers, <PERSON><PERSON>hart, Filter } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Eye, BarChart, Share2, MoreHorizontal } from "lucide-react"
import { Skeleton } from "../ui/skeleton"
import { useTranslations } from 'next-intl'

// 默认缩略图路径
const defaultThumbnail = '/images/placeholder.jpg'

interface VideoData {
  id: string
  channelId: string
  youtubeId: string
  title: string
  thumbnail: string | null
  duration: string
  publishedAt: string
  viewCount: number
  likeCount: number
  commentCount: number
  retention?: number
  ctr?: number
}

interface VideoResponse {
  data: VideoData[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

interface VideoListProps {
  channelId: string
}

// 图片加载状态类型
type ImageLoadState = 'loading' | 'loaded' | 'error'

// 封装图片优化处理逻辑
const useOptimizedImage = (originalSrc: string | null) => {
  const [imageState, setImageState] = useState<ImageLoadState>('loading')
  const [imageSrc, setImageSrc] = useState<string>(originalSrc || defaultThumbnail)
  
  // 处理YouTube缩略图加载失败的情况
  const handleImageError = useCallback(() => {
    // 如果是YouTube缩略图失败，尝试其他尺寸
    if (imageSrc.includes('ytimg.com') && imageSrc.includes('maxresdefault.jpg')) {
      // 尝试加载标准质量版本
      const standardQualityImg = imageSrc.replace('maxresdefault.jpg', 'hqdefault.jpg')
      setImageSrc(standardQualityImg)
    } else if (imageSrc.includes('ytimg.com') && imageSrc.includes('hqdefault.jpg')) {
      // 如果中等分辨率也失败，尝试加载最低质量版本
      const lowQualityImg = imageSrc.replace('hqdefault.jpg', 'mqdefault.jpg')
      setImageSrc(lowQualityImg)
    } else {
      // 如果所有尝试都失败，或者不是YouTube缩略图，显示默认图片
      setImageSrc(defaultThumbnail)
      setImageState('error')
    }
  }, [imageSrc])

  return {
    imageSrc,
    imageState,
    setImageState,
    handleImageError
  }
}

// 格式化数字为K, M单位
const formatCompactNumber = (num: number | null | undefined): string => {
  if (num === null || num === undefined) {
    return "0"
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M"
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K"
  }
  return num.toString()
}

// 格式化数字为带千位分隔符的字符串
const formatNumber = (num: number | null | undefined): string => {
  if (num === null || num === undefined) {
    return "0"
  }
  return new Intl.NumberFormat("zh-CN").format(num)
}

// 添加日期格式化函数
const formatDateTime = (dateStr: string): string => {
  try {
    const date = new Date(dateStr);
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      // hour: '2-digit',
      // minute: '2-digit'
    }).format(date);
  } catch (e) {
    return dateStr;
  }
}

// 视频卡片组件
const VideoCard = ({ video }: { video: VideoData }) => {
  const t = useTranslations('channel')
  const { imageSrc, imageState, setImageState, handleImageError } = useOptimizedImage(video.thumbnail)
  
  return (
    <div className="group p-2 sm:p-0">
      <div className="relative aspect-video rounded-lg overflow-hidden mb-3 sm:mb-4">
        {imageState === 'loading' && (
          <Skeleton className="absolute inset-0 w-full h-full" />
        )}
        <Image
          src={imageSrc}
          alt={video.title}
          fill
          loading="lazy"
          className={`object-cover group-hover:scale-105 transition-transform duration-300 ${
            imageState !== 'loaded' ? 'opacity-0' : 'opacity-100'
          }`}
          onLoad={() => setImageState('loaded')}
          onError={handleImageError}
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
        />
        {imageState === 'error' && (
          <div className="absolute inset-0 bg-slate-200 flex items-center justify-center">
            <span className="text-slate-500 text-xs sm:text-sm">{t('imageLoadError')}</span>
          </div>
        )}
        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
          {video.duration}
        </div>
      </div>
      <h3 className="font-medium line-clamp-2 group-hover:text-emerald-600 transition-colors text-sm sm:text-base">
          <a 
            href={`https://www.youtube.com/watch?v=${video.youtubeId}`}
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-blue-600 transition-colors"
          >
            {video.title}
          </a>
      </h3>
      <div className="flex items-center text-xs sm:text-sm text-slate-500 mt-2 sm:mt-3">
        <span>{formatDateTime(video.publishedAt)}</span>
        <span className="mx-2">•</span>
        <span>{t('viewCountShort', { count: formatCompactNumber(video.viewCount) })}</span>
      </div>
      <div className="flex items-center gap-3 mt-2 sm:mt-3 text-xs text-slate-500">
        <div className="flex items-center">
          <ThumbsUp className="h-3.5 w-3.5 mr-1" />
          {formatCompactNumber(video.likeCount)}
        </div>
        <div className="flex items-center">
          <MessageSquare className="h-3.5 w-3.5 mr-1" />
          {formatCompactNumber(video.commentCount)}
        </div>
      </div>
    </div>
  )
}

// 表格中的视频行组件
const VideoTableRow = ({ video }: { video: VideoData }) => {
  const t = useTranslations('channel')
  // 使用优化后的图片处理Hook
  const { imageSrc, imageState, setImageState, handleImageError } = useOptimizedImage(video.thumbnail)
  
  return (
    <TableRow>
      <TableCell className="font-medium">
        <div className="flex items-center gap-3">
          <div className="relative w-24 h-14 rounded overflow-hidden">
            {imageState === 'loading' && (
              <Skeleton className="absolute inset-0 w-full h-full" />
            )}
            <Image
              src={imageSrc}
              alt={video.title}
              fill
              loading="lazy"
              className={`object-cover ${
                imageState !== 'loaded' ? 'opacity-0' : 'opacity-100'
              }`}
              onLoad={() => setImageState('loaded')}
              onError={handleImageError}
              sizes="96px"
            />
            {imageState === 'error' && (
              <div className="absolute inset-0 bg-slate-200 flex items-center justify-center">
                <span className="text-slate-500 text-xs">{t('imageLoadError')}</span>
              </div>
            )}
          </div>
          <div className="line-clamp-2 max-w-xs">{video.title}</div>
        </div>
      </TableCell>
      <TableCell>{formatDateTime(video.publishedAt)}</TableCell>
      <TableCell className="text-right">{formatNumber(video.viewCount)}</TableCell>
      <TableCell className="text-right">
        <div className="flex items-center justify-end gap-1">
          <ThumbsUp className="h-3.5 w-3.5" />
          {formatCompactNumber(video.likeCount)}
        </div>
      </TableCell>
      {/* <TableCell className="text-right">{video.retention}%</TableCell>
      <TableCell className="text-right">{video.ctr}%</TableCell> */}
      {/* <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Eye className="h-4 w-4 mr-2" />
              查看详情
            </DropdownMenuItem>
            <DropdownMenuItem>
              <BarChart className="h-4 w-4 mr-2" />
              分析表现
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Share2 className="h-4 w-4 mr-2" />
              分享
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell> */}
    </TableRow>
  )
}

export default function VideoList({ channelId }: VideoListProps) {
  const t = useTranslations('channel')
  const tHome = useTranslations('home')
  const searchParams = useSearchParams()
  const router = useRouter()
  const [videos, setVideos] = useState<VideoData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [videoViewMode, setVideoViewMode] = useState<"grid" | "list">("grid")
  const [totalVideos, setTotalVideos] = useState(0)
  const [totalPages, setTotalPages] = useState(1)
  const [videoSortBy, setVideoSortBy] = useState("recent")
  
  // 已加载过的图片缓存，避免重复加载
  const loadedImages = useRef<Set<string>>(new Set())
  
  // 创建一个用于存储未来页面数据的缓存
  const pageCache = useRef<Map<number, VideoResponse>>(new Map())

  const currentPage = Number(searchParams.get('page')) || 1

  // 添加请求防护
  const fetchingRef = useRef(false)
  const abortControllerRef = useRef<AbortController | null>(null)

  // 使用useCallback包装fetchVideos函数，减少不必要的重新创建
  const fetchVideos = useCallback(async (page: number) => {
    // 如果有缓存，直接使用缓存
    if (pageCache.current.has(page)) {
      const cachedData = pageCache.current.get(page)!
      return cachedData
    }

    // 防止重复请求
    if (fetchingRef.current) {
      console.log('Request already in progress, skipping...')
      return null
    }

    try {
      fetchingRef.current = true

      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // 创建新的 AbortController
      abortControllerRef.current = new AbortController()

      const response = await fetch(`/api/videos?channelId=${channelId}&page=${page}&limit=12&sortBy=${videoSortBy}`, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Cache-Control': 'max-age=120', // 2分钟缓存
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch videos')
      }
      const data: VideoResponse = await response.json()

      // 缓存当前页面数据
      pageCache.current.set(page, data)

      return data
    } catch (error) {
      // 忽略被取消的请求
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Request was aborted')
        return null
      }
      console.error('Error fetching videos:', error)
      return null
    } finally {
      fetchingRef.current = false
    }
  }, [channelId, videoSortBy])

  // 加载当前页数据
  useEffect(() => {
    const loadCurrentPageData = async () => {
      setIsLoading(true)
      const data = await fetchVideos(currentPage)

      if (data) {
        setVideos(data.data)
        setTotalVideos(data.pagination.total)
        setTotalPages(data.pagination.totalPages)

        // 预加载下一页（移到这里，避免依赖循环）
        if (currentPage < data.pagination.totalPages) {
          const idleCallback = window.requestIdleCallback || ((cb) => setTimeout(cb, 1000))

          idleCallback(() => {
            fetchVideos(currentPage + 1) // 预加载但不更新状态
          })
        }
      } else {
        // Handle fetch error (e.g., set an error state)
        setVideos([])
        setTotalVideos(0)
        setTotalPages(1)
      }

      setIsLoading(false)
    }

    loadCurrentPageData()

    // 清理函数
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      fetchingRef.current = false
    }
  }, [channelId, currentPage, fetchVideos]) // 移除 totalPages 依赖

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    const params = new URLSearchParams(searchParams)
    params.set('page', page.toString())
    router.push(`?${params.toString()}`)
  }

  // 处理排序变化
  const handleSortChange = (value: string) => {
    setVideoSortBy(value)
    pageCache.current.clear() // 清除缓存
    
    // 重置到第一页
    const params = new URLSearchParams(searchParams)
    params.set('page', '1')
    router.push(`?${params.toString()}`)
  }

  // 渲染加载占位符
  const renderLoadingPlaceholders = () => {
    return Array.from({ length: 12 }).map((_, index) => (
      <div key={`skeleton-${index}`} className="space-y-3">
        <Skeleton className="w-full aspect-video rounded-lg" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
        <div className="flex gap-2">
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-16" />
        </div>
      </div>
    ))
  }

  return (
    <Card>
      <CardHeader className="p-4 sm:p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle className="text-lg sm:text-xl">{t('videoAnalysis')}</CardTitle>
            <CardDescription className="text-sm sm:text-base">{t('videoPerformanceData')}</CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="flex border rounded-md overflow-hidden">
              <Button
                variant={videoViewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setVideoViewMode("grid")}
                className="rounded-none"
              >
                <Layers className="h-4 w-4" />
              </Button>
              <Button
                variant={videoViewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setVideoViewMode("list")}
                className="rounded-none"
              >
                <LineChart className="h-4 w-4" />
              </Button>
            </div>
            <Select value={videoSortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-[140px] sm:w-[180px]">
                <SelectValue placeholder={t('sortByPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">{t('sortByRecent')}</SelectItem>
                <SelectItem value="viewCount">{t('viewCount')}</SelectItem>
                {/* <SelectItem value="engagement">{t('sortByEngagement')}</SelectItem> 
                <SelectItem value="retention">{t('sortByRetention')}</SelectItem>  */}
              </SelectContent>
            </Select>
            {/* <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              {tHome('filter')} 
            </Button> */}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 sm:p-6">
        {videoViewMode === "grid" ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 content-container">
            {isLoading
              ? renderLoadingPlaceholders()
              : videos.map((video) => <VideoCard key={video.id} video={video} />)
            }
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table className="responsive-table">
              <TableHeader>
                <TableRow>
                  <TableHead className="text-sm sm:text-base">{t('video')}</TableHead>
                  <TableHead className="text-sm sm:text-base">{t('published')}</TableHead>
                  <TableHead className="text-right text-sm sm:text-base">{t('viewCount')}</TableHead>
                  <TableHead className="text-right text-sm sm:text-base">{t('engagement')}</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 12 }).map((_, index) => (
                    <TableRow key={`skeleton-row-${index}`}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Skeleton className="w-24 h-14 rounded" />
                          <Skeleton className="h-4 w-40" />
                        </div>
                      </TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-4 w-16 ml-auto" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-4 w-16 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : (
                  videos.map((video) => <VideoTableRow key={video.id} video={video} />)
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row items-center justify-between border-t p-4 gap-4 sm:gap-0">
        <div className="text-xs sm:text-sm text-slate-500">
          {t('paginationShowing')} <span className="font-medium">{videos.length ? (currentPage - 1) * 12 + 1 : 0}</span> {t('paginationTo')}{" "}
          <span className="font-medium">{Math.min(currentPage * 12, totalVideos)}</span> {t('paginationTotal')}{" "}
          <span className="font-medium">{totalVideos}</span> {t('paginationVideosUnit')}
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-8 p-0"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1 || isLoading}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          {(() => {
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            if (endPage - startPage < 4 && totalPages >= 5) {
              startPage = Math.max(1, endPage - 4);
            }
            
            const pages = [];
            for (let i = startPage; i <= endPage; i++) {
              pages.push(i);
            }
            
            return pages.map(page => (
              <Button
                key={`page-${page}`}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => handlePageChange(page)}
                disabled={isLoading}
              >
                {page}
              </Button>
            ));
          })()}
          
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-8 p-0"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages || isLoading}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
} 