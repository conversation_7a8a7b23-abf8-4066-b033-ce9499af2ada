import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Globe, Calendar, BarChart2 } from "lucide-react";
import { useTranslations } from 'next-intl';

interface AboutTabProps {
  channelId?: string;
}

interface ChannelAboutData {
  subscriberCount: number;
  viewCount: number;
  country: string;
  category: string;
  joinDate: string | null;
  channelAge: string;
  description: string;
}

export default function AboutTab({ channelId = "TESTUCQOt3FI6FBeYecatZq7auyA" }: AboutTabProps) {
  const t = useTranslations('channel');
  const [aboutData, setAboutData] = React.useState<ChannelAboutData | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  // 使用 ref 来避免重复请求
  const fetchingRef = React.useRef(false);
  const abortControllerRef = React.useRef<AbortController | null>(null);

  React.useEffect(() => {
    const fetchAboutData = async () => {
      // 防止重复请求
      if (fetchingRef.current) {
        console.log('Request already in progress, skipping...');
        return;
      }

      try {
        fetchingRef.current = true;
        setIsLoading(true);
        setError(null);

        // 取消之前的请求
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }

        // 创建新的 AbortController
        abortControllerRef.current = new AbortController();

        console.log(`Fetching about data for channel: ${channelId}`);
        const response = await fetch(`/api/channels/${channelId}/about`, {
          signal: abortControllerRef.current.signal,
          // 添加缓存控制
          headers: {
            'Cache-Control': 'max-age=300', // 5分钟缓存
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch channel data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success && result.data) {
          console.log('About data fetched successfully:', result.data);
          setAboutData(result.data);
        } else {
          console.error('API returned unsuccessful response:', result);
          throw new Error(result.error || 'Failed to fetch channel data');
        }
      } catch (err: any) {
        // 忽略被取消的请求
        if (err.name === 'AbortError') {
          console.log('Request was aborted');
          return;
        }
        console.error('Error fetching about data:', err.message || err);
        setError(err.message || 'Failed to fetch channel data');
      } finally {
        fetchingRef.current = false;
        setIsLoading(false);
      }
    };

    if (channelId && channelId !== "TESTUCQOt3FI6FBeYecatZq7auyA") {
      fetchAboutData();
    } else if (!channelId) {
      setError('Missing channel ID');
      setIsLoading(false);
    }

    // 清理函数
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchingRef.current = false;
    };
  }, [channelId]); // 移除 t 依赖，避免翻译更新时重复请求

  // Format numbers for display
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat().format(num);
  };

  // Format compact numbers (1.5M, 72.6M)
  const formatCompactNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Empty state component
  const EmptyState = ({ message }: { message: string }) => (
    <div className="flex flex-col items-center justify-center p-8 text-center bg-muted/30 rounded-lg h-64">
      <BarChart2 className="h-16 w-16 text-muted mb-4" />
      <h3 className="text-lg font-medium mb-2">{t('noData')}</h3>
      <p className="text-muted-foreground max-w-md">{message}</p>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-80">
        <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (error) {
    return <EmptyState message={error} />;
  }

  if (!aboutData) {
    return <EmptyState message={t('noChannelData')} />;
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 content-container">
        <Card className="bg-card">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-muted-foreground mb-2">{t('views')}</h3>
              <p className="text-3xl font-bold">{formatCompactNumber(aboutData.viewCount)}</p>
              {/* <p className="text-sm text-muted-foreground mt-1">#{formatNumber(243689)}</p> */}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-muted-foreground mb-2">{t('subscribers')}</h3>
              <p className="text-3xl font-bold">{formatCompactNumber(aboutData.subscriberCount)}</p>
              {/* <p className="text-sm text-muted-foreground mt-1">#{formatNumber(1254)}</p> */}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-muted-foreground mb-2">{t('country')}</h3>
              <p className="text-3xl font-bold">{aboutData.country}</p>
              {/* <p className="text-sm text-muted-foreground mt-1">#{formatNumber(1013)}</p> */}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-muted-foreground mb-2">{t('category')}</h3>
              <p className="text-xl font-bold">{aboutData.category}</p>
              {/* <p className="text-sm text-muted-foreground mt-1">#{formatNumber(339)}</p> */}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Channel Details */}
      <Card className="bg-card">
        <CardHeader>
          <CardTitle>{t('channelDetails')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <Globe className="h-5 w-5 text-muted-foreground mr-3" />
                <div>
                  <p className="text-muted-foreground">{t('category')}</p>
                  <p className="font-medium">{aboutData.category}</p>
                </div>
              </div>
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-muted-foreground mr-3" />
                <div>
                  <p className="text-muted-foreground">{t('channelAge')}</p>
                  <p className="font-medium">{aboutData.channelAge}</p>
                </div>
              </div>
            </div>
            {aboutData.joinDate && (
              <div className="pt-2">
                <p className="text-muted-foreground text-sm">{t('since')} {new Date(aboutData.joinDate).toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
              </div>
            )}

            {aboutData.description && (
              <div className="pt-4 border-t border-border mt-4">
                <h2 className="text-sm font-medium mb-2">{t('description')}</h2>
                <p className="text-sm font-bold text-muted-foreground whitespace-pre-wrap">{aboutData.description}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
