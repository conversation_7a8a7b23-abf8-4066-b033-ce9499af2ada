import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { differenceInYears } from 'date-fns';



// Function to determine category from topicCategories
function determineCategory(topicCategories: string[] | string | null): string {
  if (!topicCategories) return "Uncategorized";

  // Handle both array and string formats
  let categories: string[];
  if (Array.isArray(topicCategories)) {
    categories = topicCategories;
  } else if (typeof topicCategories === 'string') {
    // Parse the topic categories string (fallback for string format)
    categories = topicCategories.split(',').map(cat => cat.trim());
  } else {
    return "Uncategorized";
  }

  // If no categories, return default
  if (categories.length === 0) return "Uncategorized";

  // Return the first category directly (as requested by user)
  // For example: {Entertainment,"Pop music",Music} -> "Entertainment"
  const firstCategory = categories[0].trim();

  // Remove quotes if present (handle cases like "Pop music")
  const cleanCategory = firstCategory.replace(/^["']|["']$/g, '');

  return cleanCategory || "Other";
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ channelId: string }> }
) {
  try {
    const { channelId } = await params;

    if (!channelId) {
      return NextResponse.json({ error: 'Channel ID is required' }, { status: 400 });
    }

    // Get channel information from database using Prisma ORM (只选择需要的字段)
    const channel = await prisma.ytbChannel.findUnique({
      where: {
        channelId: channelId,
      },
      select: {
        subscriberCount: true,
        viewCount: true,
        publishedAt: true,
        country: true,
        topicCategories: true,
        description: true,
      },
    });

    if (!channel) {
      return NextResponse.json({ error: 'Channel not found' }, { status: 404 });
    }

    // Calculate channel age
    const channelAge = channel.publishedAt
      ? `${differenceInYears(new Date(), new Date(channel.publishedAt))} years`
      : "Unknown";

    // Determine category from topicCategories
    const category = determineCategory(channel.topicCategories);

    // Prepare response data (直接处理 BigInt 转换以提高性能)
    const aboutData = {
      subscriberCount: channel.subscriberCount || 0,
      viewCount: channel.viewCount ? channel.viewCount.toString() : "0",
      country: channel.country || "Unknown",
      category: category,
      joinDate: channel.publishedAt ? channel.publishedAt.toISOString() : null,
      channelAge: channelAge,
      description: channel.description || "",
    };

    // Return data with cache headers (无需序列化)
    const response = NextResponse.json({
      success: true,
      data: aboutData,
    });

    // 添加缓存头，缓存10分钟（about 信息变化较少）
    response.headers.set('Cache-Control', 'public, max-age=600, stale-while-revalidate=1200');
    response.headers.set('CDN-Cache-Control', 'public, max-age=600');
    response.headers.set('Vercel-CDN-Cache-Control', 'public, max-age=600');

    return response;
  } catch (error) {
    console.error(`Error fetching channel about data: ${error}`);
    return NextResponse.json(
      { error: 'Failed to fetch channel about data' },
      { status: 500 }
    );
  }
}
