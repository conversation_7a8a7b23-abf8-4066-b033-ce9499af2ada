import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { format, subDays } from 'date-fns';

// 直接定义翻译对象
const translations = {
  zh: {
    summary: {
      dailyAverage: '日均',
      weeklyAverage: '周均',
      last28Days: '近28天'
    },
    errors: {
      missingChannelId: '频道ID是必需的',
      fetchFailed: '获取频道图表数据失败'
    }
  },
  en: {
    summary: {
      dailyAverage: 'Daily Average',
      weeklyAverage: 'Weekly Average',
      last28Days: 'Last 28 Days'
    },
    errors: {
      missingChannelId: 'Channel ID is required',
      fetchFailed: 'Failed to fetch channel chart data'
    }
  }
};

// 辅助函数，格式化日期
function formatDate(date: Date, formatStr: string = 'yyyy-MM-dd'): string {
  return format(date, formatStr);
}

// 辅助函数，格式化数字为带K或M单位的字符串
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// 获取频道图表数据
export async function GET(
  request: NextRequest,
  { params }: { params: { channelId: string } }
) {
  try {
    
    // 确定语言环境
    const acceptLanguage = request.headers.get('accept-language');
    const requestLocale = acceptLanguage?.split(',')?.[0]?.split('-')?.[0] || 'en'; // 默认 'zh'
    
    // 选择对应语言的翻译
    const locale = request.cookies.get('NEXT_LOCALE')?.value || 'en';
    // const locale = requestLocale.startsWith('zh') ? 'zh' : 'en';
    const messages = translations[locale as keyof typeof translations];
    
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d'; // 默认30天
    
    // 在Next.js App Router中，应该等待params对象
    const { channelId } = await params;

    if (!channelId) {
      return NextResponse.json({ error: messages.errors.missingChannelId }, { status: 400 });
    }

    // 根据timeRange确定时间范围和period_type
    const periodType = timeRange === '7d' ? 'day' : 'week';
    const days = timeRange === '7d' ? 7 : 30;

    // 使用 Promise.all 并行查询以提高性能
    const [trendData, dailyData] = await Promise.all([
      // 获取趋势数据（观看量和订阅人数）
      prisma.ytb_channel_time_statistics.findMany({
        where: {
          channel_id: channelId,
          period_type: periodType,
          deleted_at: null,
          period_end: {
            gte: subDays(new Date(), days),
          },
        },
        orderBy: {
          period_end: 'asc',
        },
        select: {
          period_end: true,
          end_views: true,
          end_subscribers: true,
          view_change: true,
          subscriber_change: true,
          view_growth_rate: true,
          subscriber_growth_rate: true,
          revenue_change: true,
        },
      }),

      // 获取近14天的详细数据
      prisma.ytb_channel_time_statistics.findMany({
        where: {
          channel_id: channelId,
          period_type: 'day',
          deleted_at: null,
          period_end: {
            gte: subDays(new Date(), 14),
          },
        },
        orderBy: {
          period_end: 'desc',
        },
        take: 14,
        select: {
          period_end: true,
          subscriber_change: true,
          end_subscribers: true,
          view_change: true,
          end_views: true,
          revenue_change: true,
        },
      })
    ]);

    // 转换趋势数据为图表格式
    const chartData = trendData.map(item => {
      return {
        date: formatDate(item.period_end, 'MM-dd'),
        views: Number(item.end_views || 0),
        subscribers: Number(item.end_subscribers || 0),
        viewsChange: Number(item.view_change || 0),
        subscribersChange: Number(item.subscriber_change || 0),
      };
    });

    // 转换成表格所需的格式
    const channelDailyData = dailyData.map(day => {
      // 获取星期几
      const dayOfWeek = format(day.period_end, 'EEE');
      
      // 格式化日期
      const formattedDate = format(day.period_end, 'MM/dd/yyyy');
      
      // 将BigInt和Decimal转换为Number处理
      const subscriberChange = Number(day.subscriber_change);
      const viewChange = Number(day.view_change);
      const revenueChange = Number(day.revenue_change);
      
      // 订阅者变化格式化（如果为正，加上+号）
      const subsChange = subscriberChange > 0 
        ? `+${subscriberChange}` 
        : subscriberChange === 0 
          ? "-" 
          : subscriberChange.toString();
      
      // 观看变化格式化
      const viewsChange = viewChange > 0 
        ? `+${formatNumber(viewChange)}` 
        : viewChange === 0 
          ? "-" 
          : formatNumber(viewChange);
      
      // 收益范围格式化
      const revenueRange = revenueChange > 0 
        ? `¥${revenueChange.toFixed(2)}` 
        : "-";
      
      return {
        day: dayOfWeek,
        date: formattedDate,
        subsChange,
        subsTotal: Number(day.end_subscribers),
        viewsChange,
        viewsTotal: Number(day.end_views),
        revenueRange,
        isLive: false, // 这里可以根据实际情况判断是否为直播日
      };
    });

    // 计算汇总数据
    const calculateAverage = (data: typeof dailyData, field: 'subscriber_change' | 'view_change' | 'revenue_change') => {
      const sum = data.reduce((acc, curr) => acc + Number(curr[field] || 0), 0);
      return sum / (data.length || 1);
    };

    const dailyAvgSubscribers = Math.round(calculateAverage(dailyData, 'subscriber_change'));
    const dailyAvgViews = Math.round(calculateAverage(dailyData, 'view_change'));
    const dailyAvgRevenue = calculateAverage(dailyData, 'revenue_change');

    // 每周平均（将dailyAvg乘以7）
    const weeklyAvgSubscribers = dailyAvgSubscribers * 7;
    const weeklyAvgViews = dailyAvgViews * 7;
    const weeklyAvgRevenue = dailyAvgRevenue * 7;

    // 28天总计（将dailyAvg乘以28）
    const monthlyTotalSubscribers = dailyAvgSubscribers * 28;
    const monthlyTotalViews = dailyAvgViews * 28;
    const monthlyTotalRevenue = dailyAvgRevenue * 28;

    // 构建汇总数据
    const summaryData = [
      { 
        label: messages.summary.dailyAverage, 
        subsChange: dailyAvgSubscribers > 0 ? `+${dailyAvgSubscribers}` : dailyAvgSubscribers.toString(),
        viewsChange: dailyAvgViews > 0 ? `+${formatNumber(dailyAvgViews)}` : formatNumber(dailyAvgViews),
        revenueRange: `¥${dailyAvgRevenue.toFixed(2)}` 
      },
      { 
        label: messages.summary.weeklyAverage, 
        subsChange: weeklyAvgSubscribers > 0 ? `+${weeklyAvgSubscribers}` : weeklyAvgSubscribers.toString(),
        viewsChange: weeklyAvgViews > 0 ? `+${formatNumber(weeklyAvgViews)}` : formatNumber(weeklyAvgViews),
        revenueRange: `¥${weeklyAvgRevenue.toFixed(2)}` 
      },
      { 
        label: messages.summary.last28Days, 
        subsChange: monthlyTotalSubscribers > 0 ? `+${monthlyTotalSubscribers}` : monthlyTotalSubscribers.toString(),
        viewsChange: monthlyTotalViews > 0 ? `+${formatNumber(monthlyTotalViews)}` : formatNumber(monthlyTotalViews), 
        revenueRange: `¥${monthlyTotalRevenue.toFixed(2)}` 
      },
    ];

    // 添加缓存头
    const response = NextResponse.json({
      success: true,
      data: {
        chartData,
        channelDailyData,
        summaryData,
      }
    });

    // 缓存5分钟
    response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=600');
    response.headers.set('CDN-Cache-Control', 'public, max-age=300');

    return response;
  } catch (error) {
    console.error(`Error fetching chart data: ${error}`);
    
    // 确定语言环境（在catch块中需要重新获取）
    const acceptLanguage = request.headers.get('accept-language');
    const requestLocale = acceptLanguage?.split(',')?.[0]?.split('-')?.[0] || 'zh';
    const locale = requestLocale.startsWith('zh') ? 'zh' : 'en';
    const messages = translations[locale];
    
    return NextResponse.json(
      { error: messages.errors.fetchFailed },
      { status: 500 }
    );
  }
} 