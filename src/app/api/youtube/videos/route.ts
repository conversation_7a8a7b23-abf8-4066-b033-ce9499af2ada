import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

/**
 * YouTube Data API v3 视频信息获取接口
 * 使用 Google 官方 googleapis 库获取视频详细信息
 */

const API_KEY = process.env.YOUTUBE_API_KEY;

// 视频信息接口类型定义
interface YouTubeVideoResponse {
  kind: string;
  etag: string;
  pageInfo: {
    totalResults: number;
    resultsPerPage: number;
  };
  items: YouTubeVideo[];
}

interface YouTubeVideo {
  kind: string;
  etag: string;
  id: string;
  snippet: {
    publishedAt: string;
    channelId: string;
    title: string;
    description: string;
    thumbnails: {
      default: { url: string; width: number; height: number };
      medium: { url: string; width: number; height: number };
      high: { url: string; width: number; height: number };
      standard?: { url: string; width: number; height: number };
      maxres?: { url: string; width: number; height: number };
    };
    channelTitle: string;
    tags?: string[];
    categoryId: string;
    liveBroadcastContent: string;
    defaultLanguage?: string;
    defaultAudioLanguage?: string;
  };
  statistics: {
    viewCount: string;
    likeCount: string;
    favoriteCount: string;
    commentCount: string;
  };
  contentDetails: {
    duration: string;
    dimension: string;
    definition: string;
    caption: string;
    licensedContent: boolean;
    contentRating: object;
    projection: string;
  };
}

/**
 * GET /api/youtube/videos
 * 获取 YouTube 视频信息
 * 
 * 查询参数:
 * - id: 视频ID (必需)
 * - part: 返回的数据部分，默认为 'snippet,statistics,contentDetails'
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  console.log(`[YouTube Videos API] 🚀 开始处理视频请求`);

  try {
    // 检查 API Key 是否配置
    console.log(`[YouTube Videos API] 🔑 检查 API Key 配置...`);
    if (!API_KEY) {
      console.error('[YouTube Videos API] ❌ YOUTUBE_API_KEY 环境变量未配置');
      return NextResponse.json(
        {
          error: 'YouTube API key not configured',
          message: 'YOUTUBE_API_KEY environment variable is required'
        },
        { status: 500 }
      );
    }
    console.log(`[YouTube Videos API] ✅ API Key 已配置 (长度: ${API_KEY.length})`);

    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('id');
    const part = searchParams.get('part') || 'snippet,statistics,contentDetails';

    console.log(`[YouTube Videos API] 📋 请求参数:`);
    console.log(`[YouTube Videos API]    - videoId: ${videoId || 'null'}`);
    console.log(`[YouTube Videos API]    - part: ${part}`);

    // 验证参数
    if (!videoId) {
      console.error('[YouTube Videos API] ❌ 缺少必需参数: videoId');
      return NextResponse.json(
        {
          error: 'Missing required parameter',
          message: 'Video "id" parameter is required'
        },
        { status: 400 }
      );
    }

    console.log(`[YouTube Videos API] 🎬 开始获取视频信息 - ID: ${videoId}`);

    // 初始化 YouTube API 客户端
    console.log(`[YouTube Videos API] 🔗 初始化 Google APIs 客户端...`);
    const youtube = google.youtube({
      version: 'v3',
      auth: API_KEY
    });

    console.log(`[YouTube Videos API] 📡 调用 YouTube API...`);
    const fetchStartTime = Date.now();

    // 构建请求参数
    const requestParams = {
      part: part.split(','),
      id: [videoId],
      maxResults: 1
    };

    console.log(`[YouTube Videos API] 📋 请求参数:`, JSON.stringify(requestParams, null, 2));

    // 调用 YouTube API
    const response = await youtube.videos.list(requestParams);

    const fetchDuration = Date.now() - fetchStartTime;
    console.log(`[YouTube Videos API] ⏱️  API 请求耗时: ${fetchDuration}ms`);
    console.log(`[YouTube Videos API] 📊 响应状态: ${response.status}`);
    console.log(`[YouTube Videos API] 📦 找到 ${response.data.items?.length || 0} 个视频`);

    // 检查是否找到视频
    if (!response.data.items || response.data.items.length === 0) {
      console.log(`[YouTube Videos API] ❌ 未找到视频数据`);
      return NextResponse.json(
        {
          error: 'Video not found',
          message: `No video found for ID: ${videoId}`
        },
        { status: 404 }
      );
    }

    // 格式化返回数据
    console.log(`[YouTube Videos API] 🔄 格式化视频数据...`);
    const video = response.data.items[0];
    console.log(`[YouTube Videos API] 🎬 视频信息: ${video.snippet?.title}`);
    console.log(`[YouTube Videos API] 👀 观看数: ${video.statistics?.viewCount}`);
    console.log(`[YouTube Videos API] 👍 点赞数: ${video.statistics?.likeCount}`);

    const formattedVideo = {
      id: video.id,
      title: video.snippet?.title || '',
      description: video.snippet?.description || '',
      publishedAt: video.snippet?.publishedAt,
      channelId: video.snippet?.channelId || '',
      channelTitle: video.snippet?.channelTitle || '',
      thumbnails: video.snippet?.thumbnails || {},
      tags: video.snippet?.tags || [],
      categoryId: video.snippet?.categoryId || '',
      statistics: {
        viewCount: parseInt(video.statistics?.viewCount || '0'),
        likeCount: parseInt(video.statistics?.likeCount || '0'),
        favoriteCount: parseInt(video.statistics?.favoriteCount || '0'),
        commentCount: parseInt(video.statistics?.commentCount || '0')
      },
      contentDetails: {
        duration: video.contentDetails?.duration || '',
        dimension: video.contentDetails?.dimension || '2d',
        definition: video.contentDetails?.definition || 'sd',
        caption: video.contentDetails?.caption === 'true',
        licensedContent: video.contentDetails?.licensedContent || false
      },
      // 添加一些计算字段
      metadata: {
        viewCountFormatted: formatNumber(parseInt(video.statistics?.viewCount || '0')),
        likeCountFormatted: formatNumber(parseInt(video.statistics?.likeCount || '0')),
        commentCountFormatted: formatNumber(parseInt(video.statistics?.commentCount || '0')),
        durationFormatted: formatDuration(video.contentDetails?.duration || ''),
        publishedDaysAgo: video.snippet?.publishedAt
          ? Math.floor((Date.now() - new Date(video.snippet.publishedAt).getTime()) / (1000 * 60 * 60 * 24))
          : 0
      }
    };

    return NextResponse.json({
      success: true,
      data: formattedVideo,
      source: 'youtube_api',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const totalDuration = Date.now() - startTime;
    console.error(`[YouTube Videos API] 💥 处理请求时发生错误 (耗时: ${totalDuration}ms):`, error);
    console.error(`[YouTube Videos API] 错误堆栈:`, error instanceof Error ? error.stack : 'No stack trace');

    // 检查是否是网络连接问题
    let errorMessage = 'Unknown error occurred';
    let errorType = 'internal_error';

    if (error instanceof Error) {
      errorMessage = error.message;

      if (error.message.includes('fetch failed') || error.message.includes('ConnectTimeoutError') || error.message.includes('ECONNRESET')) {
        errorType = 'network_error';
        errorMessage = 'Network connection failed. Please check your internet connection or firewall settings. YouTube API may be blocked in your network environment.';
        console.error(`[YouTube Videos API] 🌐 网络连接问题: 无法连接到 YouTube API`);
        console.error(`[YouTube Videos API] 💡 建议: 检查网络连接、防火墙设置或代理配置`);
      } else if (error.message.includes('timeout')) {
        errorType = 'timeout_error';
        errorMessage = 'Request timeout. The YouTube API is taking too long to respond.';
      } else if (error.message.includes('quota')) {
        errorType = 'quota_error';
        errorMessage = 'YouTube API quota exceeded. Please try again later.';
      }
    }

    return NextResponse.json(
      {
        error: errorType,
        message: errorMessage,
        details: {
          duration: totalDuration,
          timestamp: new Date().toISOString(),
          suggestion: errorType === 'network_error'
            ? 'Check network connectivity, firewall settings, or try using a VPN if YouTube API is blocked in your region.'
            : 'Please try again later or contact support if the problem persists.'
        }
      },
      { status: 500 }
    );
  }
}

/**
 * 格式化数字显示
 */
function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * 格式化 ISO 8601 持续时间为可读格式
 * 例: PT4M13S -> 4:13, PT1H2M30S -> 1:02:30
 */
function formatDuration(duration: string): string {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return '0:00';

  const hours = parseInt(match[1] || '0');
  const minutes = parseInt(match[2] || '0');
  const seconds = parseInt(match[3] || '0');

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
