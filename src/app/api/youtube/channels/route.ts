import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

/**
 * YouTube Data API v3 频道信息获取接口
 * 使用 Google 官方 googleapis 库获取频道详细信息
 */

const API_KEY = process.env.YOUTUBE_API_KEY;

// 频道信息接口类型定义
interface YouTubeChannelResponse {
  kind: string;
  etag: string;
  pageInfo: {
    totalResults: number;
    resultsPerPage: number;
  };
  items: YouTubeChannel[];
}

interface YouTubeChannel {
  kind: string;
  etag: string;
  id: string;
  snippet: {
    title: string;
    description: string;
    customUrl?: string;
    publishedAt: string;
    thumbnails: {
      default: { url: string; width: number; height: number };
      medium: { url: string; width: number; height: number };
      high: { url: string; width: number; height: number };
    };
    country?: string;
  };
  statistics: {
    viewCount: string;
    subscriberCount: string;
    hiddenSubscriberCount: boolean;
    videoCount: string;
  };
  contentDetails: {
    relatedPlaylists: {
      uploads: string;
      watchHistory?: string;
      watchLater?: string;
    };
  };
  brandingSettings?: {
    channel: {
      title: string;
      description: string;
      keywords?: string;
      country?: string;
    };
    image: {
      bannerExternalUrl?: string;
    };
  };
}

/**
 * GET /api/youtube/channels
 * 获取 YouTube 频道信息
 * 
 * 查询参数:
 * - id: 频道ID (可选，与username二选一)
 * - username: 频道用户名 (可选，与id二选一)
 * - part: 返回的数据部分，默认为 'snippet,statistics,contentDetails'
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  console.log(`[YouTube API] 🚀 开始处理频道请求`);

  try {
    // 检查 API Key 是否配置
    console.log(`[YouTube API] 🔑 检查 API Key 配置...`);
    if (!API_KEY) {
      console.error('[YouTube API] ❌ YOUTUBE_API_KEY 环境变量未配置');
      return NextResponse.json(
        {
          error: 'YouTube API key not configured',
          message: 'YOUTUBE_API_KEY environment variable is required'
        },
        { status: 500 }
      );
    }
    console.log(`[YouTube API] ✅ API Key 已配置 (长度: ${API_KEY.length})`);

    const { searchParams } = new URL(request.url);
    const channelId = searchParams.get('id');
    const username = searchParams.get('username');
    const part = searchParams.get('part') || 'snippet,statistics,contentDetails,brandingSettings';

    console.log(`[YouTube API] 📋 请求参数:`);
    console.log(`[YouTube API]    - channelId: ${channelId || 'null'}`);
    console.log(`[YouTube API]    - username: ${username || 'null'}`);
    console.log(`[YouTube API]    - part: ${part}`);

    // 验证参数
    if (!channelId && !username) {
      console.error('[YouTube API] ❌ 缺少必需参数');
      return NextResponse.json(
        {
          error: 'Missing required parameter',
          message: 'Either "id" or "username" parameter is required'
        },
        { status: 400 }
      );
    }

    console.log(`[YouTube API] 🔍 开始获取频道信息 - ID: ${channelId}, Username: ${username}`);

    // 初始化 YouTube API 客户端
    console.log(`[YouTube API] 🔗 初始化 Google APIs 客户端...`);
    const youtube = google.youtube({
      version: 'v3',
      auth: API_KEY
    });

    console.log(`[YouTube API] 📡 调用 YouTube API...`);
    const fetchStartTime = Date.now();

    // 构建请求参数
    const requestParams: any = {
      part: part.split(','),
      maxResults: 1
    };

    // 根据参数类型设置查询条件
    if (channelId) {
      requestParams.id = [channelId];
      console.log(`[YouTube API] 📍 使用频道ID查询: ${channelId}`);
    } else if (username) {
      requestParams.forUsername = username;
      console.log(`[YouTube API] 👤 使用用户名查询: ${username}`);
    }

    console.log(`[YouTube API] 📋 请求参数:`, JSON.stringify(requestParams, null, 2));

    // 调用 YouTube API
    const response = await youtube.channels.list(requestParams);

    const fetchDuration = Date.now() - fetchStartTime;
    console.log(`[YouTube API] ⏱️  API 请求耗时: ${fetchDuration}ms`);
    console.log(`[YouTube API] 📊 响应状态: ${response.status}`);
    console.log(`[YouTube API] 📦 找到 ${response.data.items?.length || 0} 个频道`);

    // 检查是否找到频道
    if (!response.data.items || response.data.items.length === 0) {
      console.log(`[YouTube API] ❌ 未找到频道数据`);
      return NextResponse.json(
        {
          error: 'Channel not found',
          message: `No channel found for ${channelId ? `ID: ${channelId}` : `username: ${username}`}`
        },
        { status: 404 }
      );
    }

    // 格式化返回数据
    console.log(`[YouTube API] 🔄 格式化频道数据...`);
    const channel = response.data.items[0];
    console.log(`[YouTube API] 📺 频道信息: ${channel.snippet?.title}`);
    console.log(`[YouTube API] 📊 订阅者: ${channel.statistics?.subscriberCount}`);
    console.log(`[YouTube API] 👀 观看数: ${channel.statistics?.viewCount}`);
    console.log(`[YouTube API] 🎥 视频数: ${channel.statistics?.videoCount}`);

    const formattedChannel = {
      id: channel.id,
      title: channel.snippet?.title || '',
      description: channel.snippet?.description || '',
      customUrl: channel.snippet?.customUrl,
      publishedAt: channel.snippet?.publishedAt,
      thumbnails: channel.snippet?.thumbnails || {},
      country: channel.snippet?.country,
      statistics: {
        viewCount: parseInt(channel.statistics?.viewCount || '0'),
        subscriberCount: parseInt(channel.statistics?.subscriberCount || '0'),
        videoCount: parseInt(channel.statistics?.videoCount || '0'),
        hiddenSubscriberCount: channel.statistics?.hiddenSubscriberCount || false
      },
      contentDetails: channel.contentDetails || {},
      brandingSettings: channel.brandingSettings || {},
      // 添加一些计算字段
      metadata: {
        subscriberCountFormatted: formatNumber(parseInt(channel.statistics?.subscriberCount || '0')),
        viewCountFormatted: formatNumber(parseInt(channel.statistics?.viewCount || '0')),
        videoCountFormatted: formatNumber(parseInt(channel.statistics?.videoCount || '0')),
        createdYearsAgo: channel.snippet?.publishedAt
          ? Math.floor((Date.now() - new Date(channel.snippet.publishedAt).getTime()) / (1000 * 60 * 60 * 24 * 365))
          : 0
      }
    };

    const totalDuration = Date.now() - startTime;
    console.log(`[YouTube API] ✅ 请求处理完成，总耗时: ${totalDuration}ms`);

    return NextResponse.json({
      success: true,
      data: formattedChannel,
      source: 'youtube_api',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const totalDuration = Date.now() - startTime;
    console.error(`[YouTube API] 💥 处理请求时发生错误 (耗时: ${totalDuration}ms):`, error);
    console.error(`[YouTube API] 错误堆栈:`, error instanceof Error ? error.stack : 'No stack trace');

    // 检查是否是网络连接问题
    let errorMessage = 'Unknown error occurred';
    let errorType = 'internal_error';

    if (error instanceof Error) {
      errorMessage = error.message;

      if (error.message.includes('fetch failed') || error.message.includes('ConnectTimeoutError')) {
        errorType = 'network_error';
        errorMessage = 'Network connection failed. Please check your internet connection or firewall settings. YouTube API may be blocked in your network environment.';
        console.error(`[YouTube API] 🌐 网络连接问题: 无法连接到 YouTube API`);
        console.error(`[YouTube API] 💡 建议: 检查网络连接、防火墙设置或代理配置`);
      } else if (error.message.includes('timeout')) {
        errorType = 'timeout_error';
        errorMessage = 'Request timeout. The YouTube API is taking too long to respond.';
      }
    }

    return NextResponse.json(
      {
        error: errorType,
        message: errorMessage,
        details: {
          duration: totalDuration,
          timestamp: new Date().toISOString(),
          suggestion: errorType === 'network_error'
            ? 'Check network connectivity, firewall settings, or try using a VPN if YouTube API is blocked in your region.'
            : 'Please try again later or contact support if the problem persists.'
        }
      },
      { status: 500 }
    );
  }
}

/**
 * 格式化数字显示
 */
function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}
