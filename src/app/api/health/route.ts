import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getPerformanceReport } from '@/lib/performance';

/**
 * 健康检查 API
 * GET /api/health
 */
export async function GET(_request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // 数据库连接检查
    const dbStartTime = Date.now();
    await prisma.$queryRaw`SELECT 1 as health_check`;
    const dbDuration = Date.now() - dbStartTime;
    
    // 获取性能报告（仅在开发环境或启用性能监控时）
    let performanceReport = null;
    if (process.env.NODE_ENV === 'development' || process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
      performanceReport = getPerformanceReport();
    }
    
    const totalDuration = Date.now() - startTime;
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      database: {
        status: 'connected',
        responseTime: `${dbDuration}ms`,
      },
      api: {
        responseTime: `${totalDuration}ms`,
      },
      ...(performanceReport && { performance: performanceReport }),
    };
    
    // 设置缓存头（健康检查不需要长时间缓存）
    const response = NextResponse.json(healthData);
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    
    return response;
    
  } catch (error) {
    console.error('健康检查失败:', error);
    
    const errorData = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      error: process.env.NODE_ENV === 'development' 
        ? (error instanceof Error ? error.message : String(error))
        : '服务不可用',
      database: {
        status: 'disconnected',
      },
    };
    
    return NextResponse.json(errorData, { status: 503 });
  }
}

/**
 * 详细性能报告 API（仅开发环境）
 * GET /api/health?detailed=true
 */
export async function getDetailedHealth() {
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: '生产环境不支持详细报告' }, { status: 403 });
  }
  
  try {
    // 数据库统计信息
    const [channelCount, statsCount] = await Promise.all([
      prisma.ytbChannel.count(),
      prisma.ytb_channel_time_statistics.count(),
    ]);
    
    const performanceReport = getPerformanceReport();
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: {
        status: 'connected',
        statistics: {
          channels: channelCount,
          timeStatistics: statsCount,
        },
      },
      performance: performanceReport,
    });
    
  } catch (error) {
    console.error('详细健康检查失败:', error);
    return NextResponse.json({
      status: 'unhealthy',
      error: error instanceof Error ? error.message : String(error),
    }, { status: 503 });
  }
}
