/**
 * 性能监控工具
 * 用于监控数据库查询和 API 响应时间
 */

interface PerformanceMetrics {
  operation: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private readonly maxMetrics = 1000; // 最多保存 1000 条记录

  /**
   * 记录操作性能
   */
  record(operation: string, duration: number, success: boolean, error?: string) {
    const metric: PerformanceMetrics = {
      operation,
      duration,
      timestamp: new Date(),
      success,
      error,
    };

    this.metrics.push(metric);

    // 保持数组大小在限制内
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }

    // 生产环境中只记录慢查询和错误
    if (process.env.NODE_ENV === 'production') {
      if (!success || duration > 1000) { // 超过 1 秒的查询
        console.warn(`慢查询或错误: ${operation}, 耗时: ${duration}ms`, error ? { error } : '');
      }
    } else {
      // 开发环境记录所有查询
      console.log(`${operation}: ${duration}ms ${success ? '✓' : '✗'}`);
    }
  }

  /**
   * 获取性能统计
   */
  getStats(operation?: string) {
    const filteredMetrics = operation 
      ? this.metrics.filter(m => m.operation === operation)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return null;
    }

    const durations = filteredMetrics.map(m => m.duration);
    const successCount = filteredMetrics.filter(m => m.success).length;

    return {
      operation: operation || 'all',
      count: filteredMetrics.length,
      successRate: (successCount / filteredMetrics.length) * 100,
      avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      p95Duration: this.percentile(durations, 95),
      p99Duration: this.percentile(durations, 99),
    };
  }

  /**
   * 计算百分位数
   */
  private percentile(arr: number[], p: number): number {
    const sorted = arr.slice().sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  /**
   * 清除所有指标
   */
  clear() {
    this.metrics = [];
  }

  /**
   * 获取最近的慢查询
   */
  getSlowQueries(threshold = 1000, limit = 10) {
    return this.metrics
      .filter(m => m.duration > threshold)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

/**
 * 性能监控装饰器
 */
export function withPerformanceMonitoring<T extends any[], R>(
  operation: string,
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    let success = true;
    let error: string | undefined;

    try {
      const result = await fn(...args);
      return result;
    } catch (err) {
      success = false;
      error = err instanceof Error ? err.message : String(err);
      throw err;
    } finally {
      const duration = Date.now() - startTime;
      performanceMonitor.record(operation, duration, success, error);
    }
  };
}

/**
 * 数据库查询性能监控中间件
 */
export function createPrismaMiddleware() {
  return async (params: any, next: any) => {
    const startTime = Date.now();
    const operation = `${params.model}.${params.action}`;
    
    try {
      const result = await next(params);
      const duration = Date.now() - startTime;
      performanceMonitor.record(operation, duration, true);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      performanceMonitor.record(operation, duration, false, errorMessage);
      throw error;
    }
  };
}

/**
 * API 路由性能监控包装器
 */
export function withApiPerformanceMonitoring(
  handler: (req: any, res: any) => Promise<any>,
  routeName: string
) {
  return async (req: any, res: any) => {
    const startTime = Date.now();
    let success = true;
    let error: string | undefined;

    try {
      const result = await handler(req, res);
      return result;
    } catch (err) {
      success = false;
      error = err instanceof Error ? err.message : String(err);
      throw err;
    } finally {
      const duration = Date.now() - startTime;
      performanceMonitor.record(`API:${routeName}`, duration, success, error);
    }
  };
}

/**
 * 获取性能报告（用于健康检查端点）
 */
export function getPerformanceReport() {
  const overall = performanceMonitor.getStats();
  const slowQueries = performanceMonitor.getSlowQueries(500, 5);
  
  return {
    overall,
    slowQueries,
    timestamp: new Date().toISOString(),
  };
}
