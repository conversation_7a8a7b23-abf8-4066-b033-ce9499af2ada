import { PrismaClient } from '@prisma/client'
import { performanceMonitor } from './performance'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

// 检查数据库 URL 配置
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL 环境变量未设置');
}

// 生产环境数据库连接优化
const getDatabaseUrl = () => {
  const baseUrl = process.env.DATABASE_URL!;

  if (process.env.NODE_ENV === 'production') {
    // 生产环境连接池优化参数
    const params = new URLSearchParams({
      connection_limit: '20',        // 连接池大小
      pool_timeout: '20',           // 连接池超时（秒）
      connect_timeout: '10',        // 连接超时（秒）
      statement_timeout: '30000',   // 语句超时（毫秒）
      idle_timeout: '600',          // 空闲连接超时（秒）
    });

    // 如果 URL 已经包含参数，合并它们
    const url = new URL(baseUrl);
    params.forEach((value, key) => {
      if (!url.searchParams.has(key)) {
        url.searchParams.set(key, value);
      }
    });

    return url.toString();
  }

  // 开发环境使用较小的连接池
  const url = new URL(baseUrl);
  if (!url.searchParams.has('connection_limit')) {
    url.searchParams.set('connection_limit', '5');
  }
  if (!url.searchParams.has('pool_timeout')) {
    url.searchParams.set('pool_timeout', '30');
  }

  return url.toString();
};

// 创建Prisma客户端实例，根据环境优化配置
export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  // 生产环境只记录错误，开发环境记录查询、错误和警告
  log: process.env.NODE_ENV === 'production'
    ? ['error']
    : ['query', 'error', 'warn'],

  datasources: {
    db: {
      url: getDatabaseUrl(),
    },
  },

  // 注意：连接池配置通过环境变量和URL参数设置
  // 生产环境建议: DATABASE_URL=postgresql://user:password@host:port/db?connection_limit=20&pool_timeout=20&connect_timeout=10&statement_timeout=30000
  // 开发环境建议: DATABASE_URL=postgresql://user:password@host:port/db?connection_limit=5&pool_timeout=30
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// 数据库操作重试包装器
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  retryDelay = 500
): Promise<T> {
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // 只重试连接相关错误
      if (!(
        error instanceof Error && 
        (error.message.includes('connection') || 
         error.message.includes('timeout') ||
         error.message.includes('pool'))
      )) {
        throw error;
      }
      
      // 生产环境中减少详细错误日志
      if (process.env.NODE_ENV === 'development') {
        console.error(`数据库操作失败(尝试 ${attempt + 1}/${maxRetries}):`, error);
      } else {
        console.error(`数据库操作失败(尝试 ${attempt + 1}/${maxRetries}):`, error.message);
      }
      
      // 指数退避重试延迟
      const delay = retryDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError || new Error('数据库操作重试失败');
}

// 确保数据库连接函数（生产环境优化版本）
export async function ensureConnection() {
  // 生产环境中减少连接检查频率，避免不必要的性能开销
  if (process.env.NODE_ENV === 'production') {
    // 生产环境中，Prisma 会自动管理连接池，通常不需要手动检查
    // 只在真正需要时才进行连接检查
    return true;
  }

  try {
    // 开发环境中执行简单查询验证连接
    await prisma.$queryRaw`SELECT 1 as connectivity_check`;
    return true;
  } catch (error) {
    console.error('数据库连接检查失败:', error);

    // 尝试重新连接
    try {
      await prisma.$disconnect();
      await prisma.$connect();
      return true;
    } catch (reconnectError) {
      console.error('数据库重连失败:', reconnectError);
      return false;
    }
  }
}

// 扩展类型，处理password字段问题
export type UserWithPassword = {
  password?: string;
} & Awaited<ReturnType<typeof prisma.user.findFirst>>;

/**
 * 扩展的findUser方法，支持使用password字段查询和返回
 */
export async function findUserWithPassword(email: string, provider: string) {
  // 这里使用any绕过TypeScript类型检查
  const user = await withRetry(async () => {
    return await prisma.user.findFirst({
      where: {
        email,
        signinProvider: provider,
        isDeleted: false,
      } as any,
    }) as UserWithPassword;
  });
  
  return user;
}

/**
 * 创建用户，支持password字段
 */
export async function createUserWithPassword(userData: { 
  uuid: string;
  email: string;
  password: string;
  signinProvider: string;
  nickname?: string;
}) {
  // 使用any绕过类型检查
  const user = await withRetry(async () => {
    return await prisma.user.create({
      data: {
        uuid: userData.uuid,
        email: userData.email,
        password: userData.password,
        signinProvider: userData.signinProvider,
        nickname: userData.nickname,
        isDeleted: false,
      } as any,
    });
  });
  
  return user;
}

/**
 * 优雅关闭数据库连接
 * 在应用退出时调用此函数可确保所有查询完成并正确关闭连接
 */
export async function disconnectPrisma() {
  await prisma.$disconnect();
}
