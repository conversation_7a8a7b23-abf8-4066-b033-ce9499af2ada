generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model YtbRawFetch {
  id        BigInt    @id @default(autoincrement())
  taskId    String    @map("task_id") @db.Uuid
  endpoint  String    @db.VarChar(255)
  response  Json
  params    Json?
  etag      String?   @db.VarChar(255)
  quotaCost Int       @default(1) @map("quota_cost")
  fetchedAt DateTime  @default(now()) @map("fetched_at") @db.Timestamptz(6)
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(6)

  @@index([endpoint], map: "idx_ytb_raw_fetch_endpoint")
  @@index([taskId], map: "idx_ytb_raw_fetch_task_id")
  @@map("ytb_raw_fetch")
}

model YtbTask {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  taskType     String    @map("task_type") @db.VarChar(50)
  status       String    @default("pending") @db.VarChar(20)
  parameters   Json
  result       Json?
  errorMessage String?   @map("error_message")
  startedAt    DateTime? @map("started_at") @db.Timestamptz(6)
  completedAt  DateTime? @map("completed_at") @db.Timestamptz(6)
  retryCount   Int       @default(0) @map("retry_count")
  nextRetryAt  DateTime? @map("next_retry_at") @db.Timestamptz(6)
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt    DateTime? @map("deleted_at") @db.Timestamptz(6)

  @@index([createdAt], map: "idx_ytb_tasks_created_at")
  @@index([nextRetryAt], map: "idx_ytb_tasks_next_retry_at")
  @@index([status], map: "idx_ytb_tasks_status")
  @@map("ytb_tasks")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model YtbChannel {
  id                          BigInt                        @id @default(autoincrement())
  channelId                   String                        @unique @map("channel_id") @db.VarChar(50)
  title                       String                        @db.VarChar(255)
  description                 String?
  customUrl                   String?                       @map("custom_url") @db.VarChar(100)
  publishedAt                 DateTime?                     @map("published_at") @db.Timestamptz(6)
  country                     String?                       @db.VarChar(2)
  viewCount                   BigInt?                       @map("view_count")
  subscriberCount             Int?                          @map("subscriber_count")
  videoCount                  Int?                          @map("video_count")
  thumbnailUrl                String?                       @map("thumbnail_url")
  bannerUrl                   String?                       @map("banner_url")
  lastRefreshedAt             DateTime                      @default(now()) @map("last_refreshed_at") @db.Timestamptz(6)
  isVerified                  Boolean?                      @default(false) @map("is_verified")
  createdAt                   DateTime                      @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                   DateTime                      @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt                   DateTime?                     @map("deleted_at") @db.Timestamptz(6)
  topicCategories             String[]                      @map("topic_categories")
  statsSnapshots              YtbChannelStatsSnapshot[]
  ytb_channel_time_statistics ytb_channel_time_statistics[]

  @@index([channelId], map: "idx_ytb_channels_channel_id")
  @@index([lastRefreshedAt], map: "idx_ytb_channels_last_refreshed_at")
  @@index([deletedAt, customUrl], map: "idx_ytb_channels_custom_url")
  @@index([deletedAt, customUrl(sort: Desc)], map: "idx_ytb_channels_custom_url_sorted")
  @@index([customUrl(ops: raw("gin_trgm_ops"))], map: "idx_ytb_channels_custom_url_trgm", type: Gin)
  @@map("ytb_channels")
}

model YtbChannelStatsSnapshot {
  id              BigInt     @id @default(autoincrement())
  channelId       String     @map("channel_id") @db.VarChar(50)
  viewCount       BigInt     @default(0) @map("view_count")
  subscriberCount Int        @default(0) @map("subscriber_count")
  videoCount      Int        @default(0) @map("video_count")
  snapshotAt      DateTime   @default(now()) @map("snapshot_at") @db.Timestamptz(6)
  createdAt       DateTime   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt       DateTime   @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt       DateTime?  @map("deleted_at") @db.Timestamptz(6)
  channel         YtbChannel @relation(fields: [channelId], references: [channelId], onDelete: NoAction, onUpdate: NoAction, map: "fk_channel_stats_channel_id")

  @@index([channelId, snapshotAt], map: "idx_ytb_channel_stats_channel_id_snapshot_at")
  @@map("ytb_channel_stats_snapshots")
}

model YtbVideo {
  id                        BigInt                   @id @default(autoincrement())
  youtubeId                 String                   @unique @map("youtube_id") @db.VarChar(50)
  channelId                 String                   @map("channel_id") @db.VarChar(50)
  title                     String                   @db.VarChar(255)
  description               String?
  publishedAt               DateTime?                @map("published_at") @db.Timestamptz(6)
  duration                  Unsupported("interval")?
  dimension                 String?                  @db.VarChar(10)
  caption                   Boolean?                 @default(false)
  licensedContent           Boolean?                 @default(false) @map("licensed_content")
  tags                      String[]
  thumbnails                Json?
  categoryId                String?                  @map("category_id") @db.VarChar(50)
  thumbnailUrl              String?                  @map("thumbnail_url")
  viewCount                 BigInt?                  @map("view_count")
  likeCount                 Int?                     @map("like_count")
  dislikeCount              Int?                     @map("dislike_count")
  favoriteCount             Int?                     @map("favorite_count")
  commentCount              Int?                     @map("comment_count")
  liveBroadcastContent      String?                  @map("live_broadcast_content") @db.VarChar(20)
  privacyStatus             String?                  @map("privacy_status") @db.VarChar(20)
  etag                      String?                  @db.VarChar(255)
  pageInfo                  Json?                    @map("page_info")
  rawData                   Json?                    @map("raw_data")
  lastRefreshedAt           DateTime                 @default(now()) @map("last_refreshed_at") @db.Timestamptz(6)
  createdAt                 DateTime                 @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                 DateTime                 @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt                 DateTime?                @map("deleted_at") @db.Timestamptz(6)
  ytb_video_stats_snapshots YtbVideoStatsSnapshot[]

  @@index([channelId], map: "idx_ytb_videos_channel_id")
  @@index([lastRefreshedAt], map: "idx_ytb_videos_last_refreshed_at")
  @@index([publishedAt], map: "idx_ytb_videos_published_at")
  @@index([youtubeId], map: "idx_ytb_videos_youtube_id")
  @@map("ytb_videos")
}

model YtbVideoStatsSnapshot {
  id               BigInt    @id @default(autoincrement())
  video_youtube_id String    @db.VarChar(50)
  viewCount        BigInt    @default(0) @map("view_count")
  likeCount        Int       @default(0) @map("like_count")
  dislikeCount     Int       @default(0) @map("dislike_count")
  favoriteCount    Int       @default(0) @map("favorite_count")
  commentCount     Int       @default(0) @map("comment_count")
  snapshotAt       DateTime  @default(now()) @map("snapshot_at") @db.Timestamptz(6)
  createdAt        DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt        DateTime? @map("deleted_at") @db.Timestamptz(6)
  ytb_videos       YtbVideo  @relation(fields: [video_youtube_id], references: [youtubeId], onDelete: Cascade, onUpdate: NoAction, map: "fk_ytb_video_stats_video_youtube_id")

  @@index([video_youtube_id, snapshotAt], map: "idx_ytb_video_stats_video_youtube_id_snapshot_at")
  @@map("ytb_video_stats_snapshots")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ytb_channel_time_statistics {
  id                     BigInt     @id @default(autoincrement())
  channel_id             String     @db.VarChar(50)
  period_type            String     @db.VarChar(20)
  period_start           DateTime   @db.Date
  period_end             DateTime   @db.Date
  start_subscribers      Int        @default(0)
  end_subscribers        Int        @default(0)
  subscriber_change      Int        @default(0)
  subscriber_growth_rate Decimal?   @db.Decimal(6, 2)
  start_views            BigInt     @default(0)
  end_views              BigInt     @default(0)
  view_change            BigInt     @default(0)
  view_growth_rate       Decimal?   @db.Decimal(6, 2)
  start_revenue          Decimal    @default(0) @db.Decimal(12, 2)
  end_revenue            Decimal    @default(0) @db.Decimal(12, 2)
  revenue_change         Decimal    @default(0) @db.Decimal(12, 2)
  revenue_growth_rate    Decimal?   @db.Decimal(6, 2)
  year                   Int?
  quarter                Int?
  month                  Int?
  week                   Int?
  day                    Int?
  created_at             DateTime   @default(now()) @db.Timestamptz(6)
  updated_at             DateTime   @default(now()) @db.Timestamptz(6)
  deleted_at             DateTime?  @db.Timestamptz(6)
  ytb_channels           YtbChannel @relation(fields: [channel_id], references: [channelId], onDelete: NoAction, onUpdate: NoAction)

  @@index([channel_id], map: "idx_channel_time_stats_channel_id")
  @@index([year, quarter, month, week, day], map: "idx_channel_time_stats_date_parts")
  @@index([period_type, period_start, period_end], map: "idx_channel_time_stats_period")
  @@index([channel_id, period_type, deleted_at, period_end(sort: Desc)], map: "idx_channel_time_stats_optimized")
  @@index([channel_id, period_type, period_end(sort: Desc)], map: "idx_channel_time_stats_with_sort")
}

model Order {
  id               Int       @id @default(autoincrement())
  orderNo          String    @unique @map("order_no") @db.VarChar(255)
  createdAt        DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  isDeleted        Boolean   @default(false) @map("is_deleted")
  userUuid         String    @map("user_uuid") @db.VarChar(255)
  userEmail        String    @map("user_email") @db.VarChar(255)
  amount           Int
  interval         String?   @db.VarChar(50)
  expiredAt        DateTime? @map("expired_at") @db.Timestamptz(6)
  status           String    @db.VarChar(50)
  stripeSessionId  String?   @map("stripe_session_id") @db.VarChar(255)
  credits          Int
  currency         String?   @db.VarChar(50)
  subId            String?   @map("sub_id") @db.VarChar(255)
  subIntervalCount Int?      @map("sub_interval_count")
  subCycleAnchor   Int?      @map("sub_cycle_anchor")
  subPeriodEnd     Int?      @map("sub_period_end")
  subPeriodStart   Int?      @map("sub_period_start")
  subTimes         Int?      @map("sub_times")
  productId        String?   @map("product_id") @db.VarChar(255)
  productName      String?   @map("product_name") @db.VarChar(255)
  validMonths      Int?      @map("valid_months")
  orderDetail      String?   @map("order_detail")
  paidAt           DateTime? @map("paid_at") @db.Timestamptz(6)
  paidEmail        String?   @map("paid_email") @db.VarChar(255)
  paidDetail       String?   @map("paid_detail")
  user             User      @relation(fields: [userUuid], references: [uuid])

  @@map("orders")
}

model system_config {
  id          Int      @id @default(autoincrement())
  key         String   @unique @db.VarChar(100)
  value       String
  description String?
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  updated_at  DateTime @db.Timestamptz(6)

  @@index([key])
}

model system_log {
  id         Int      @id @default(autoincrement())
  user_id    String?  @db.VarChar(255)
  action     String   @db.VarChar(50)
  target     String   @db.VarChar(50)
  target_id  String?  @db.VarChar(255)
  detail     String?
  ip         String?  @db.VarChar(50)
  created_at DateTime @default(now()) @db.Timestamptz(6)

  @@index([action])
  @@index([created_at])
  @@index([target])
  @@index([user_id, created_at])
  @@index([user_id])
}

model User {
  id             Int           @id @default(autoincrement())
  uuid           String        @unique
  email          String
  password       String?       @db.VarChar(255)
  createdAt      DateTime      @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime      @updatedAt @map("updated_at") @db.Timestamptz(6)
  isDeleted      Boolean       @default(false) @map("is_deleted")
  nickname       String?       @db.VarChar(255)
  avatarUrl      String?       @map("avatar_url") @db.VarChar(255)
  locale         String?       @db.VarChar(50)
  signinType     String?       @map("signin_type") @db.VarChar(50)
  signinIp       String?       @map("signin_ip") @db.VarChar(255)
  signinProvider String?       @map("signin_provider") @db.VarChar(50)
  signinOpenid   String?       @map("signin_openid") @db.VarChar(255)
  orders         Order[]
  user_family    user_family[]

  @@unique([email, signinProvider])
  @@map("users")
}

model family {
  id            Int             @id @default(autoincrement())
  uuid          String          @unique
  name          String          @db.VarChar(100)
  description   String?
  location      String?         @db.VarChar(255)
  createdBy     String          @db.VarChar(255)
  created_at    DateTime        @default(now()) @db.Timestamptz(6)
  updated_at    DateTime        @db.Timestamptz(6)
  is_deleted    Boolean         @default(false)
  family_event  family_event[]
  family_member family_member[]
  family_photo  family_photo[]
  user_family   user_family[]

  @@index([createdBy])
  @@index([location])
  @@index([name])
}

model family_education {
  id            Int           @id @default(autoincrement())
  uuid          String        @unique
  member_id     Int
  school        String        @db.VarChar(100)
  major         String?       @db.VarChar(100)
  degree        String?       @db.VarChar(50)
  start_date    DateTime      @db.Timestamptz(6)
  end_date      DateTime?     @db.Timestamptz(6)
  description   String?
  created_at    DateTime      @default(now()) @db.Timestamptz(6)
  updated_at    DateTime      @db.Timestamptz(6)
  is_deleted    Boolean       @default(false)
  family_member family_member @relation(fields: [member_id], references: [id])

  @@index([degree])
  @@index([end_date])
  @@index([member_id])
  @@index([member_id, start_date])
  @@index([school])
  @@index([start_date])
}

model family_event {
  id            Int           @id @default(autoincrement())
  uuid          String        @unique
  family_id     Int
  member_id     Int
  event_type    String        @db.VarChar(50)
  title         String        @db.VarChar(100)
  description   String?
  event_date    DateTime      @db.Timestamptz(6)
  location      String?       @db.VarChar(255)
  created_at    DateTime      @default(now()) @db.Timestamptz(6)
  updated_at    DateTime      @db.Timestamptz(6)
  is_deleted    Boolean       @default(false)
  family        family        @relation(fields: [family_id], references: [id])
  family_member family_member @relation(fields: [member_id], references: [id])

  @@index([event_date])
  @@index([event_type])
  @@index([family_id, event_date])
  @@index([family_id])
  @@index([member_id, event_date])
  @@index([member_id])
}

model family_member {
  id                                                        Int                @id @default(autoincrement())
  uuid                                                      String             @unique
  family_id                                                 Int
  name                                                      String             @db.VarChar(50)
  gender                                                    String             @db.VarChar(10)
  birth_date                                                DateTime?          @db.Timestamptz(6)
  death_date                                                DateTime?          @db.Timestamptz(6)
  avatar_url                                                String?            @db.VarChar(255)
  bio                                                       String?
  phone                                                     String?            @db.VarChar(20)
  email                                                     String?            @db.VarChar(100)
  address                                                   String?            @db.VarChar(255)
  generation                                                Int
  is_alive                                                  Boolean            @default(true)
  created_at                                                DateTime           @default(now()) @db.Timestamptz(6)
  updated_at                                                DateTime           @db.Timestamptz(6)
  is_deleted                                                Boolean            @default(false)
  family_education                                          family_education[]
  family_event                                              family_event[]
  family                                                    family             @relation(fields: [family_id], references: [id])
  family_photo                                              family_photo[]
  family_relation_family_relation_member_idTofamily_member  family_relation[]  @relation("family_relation_member_idTofamily_member")
  family_relation_family_relation_related_idTofamily_member family_relation[]  @relation("family_relation_related_idTofamily_member")
  family_work                                               family_work[]

  @@index([birth_date])
  @@index([death_date])
  @@index([family_id, birth_date])
  @@index([family_id, generation])
  @@index([generation])
  @@index([is_alive])
  @@index([name])
}

model family_photo {
  id            Int           @id @default(autoincrement())
  uuid          String        @unique
  family_id     Int
  member_id     Int
  url           String        @db.VarChar(255)
  description   String?
  taken_date    DateTime?     @db.Timestamptz(6)
  location      String?       @db.VarChar(255)
  created_at    DateTime      @default(now()) @db.Timestamptz(6)
  updated_at    DateTime      @db.Timestamptz(6)
  is_deleted    Boolean       @default(false)
  family        family        @relation(fields: [family_id], references: [id])
  family_member family_member @relation(fields: [member_id], references: [id])

  @@index([family_id])
  @@index([member_id])
  @@index([taken_date])
}

model family_relation {
  id                                                      Int           @id @default(autoincrement())
  uuid                                                    String        @unique
  member_id                                               Int
  related_id                                              Int
  relation_type                                           String        @db.VarChar(50)
  title                                                   String?       @db.VarChar(50)
  created_at                                              DateTime      @default(now()) @db.Timestamptz(6)
  updated_at                                              DateTime      @db.Timestamptz(6)
  is_deleted                                              Boolean       @default(false)
  family_member_family_relation_member_idTofamily_member  family_member @relation("family_relation_member_idTofamily_member", fields: [member_id], references: [id])
  family_member_family_relation_related_idTofamily_member family_member @relation("family_relation_related_idTofamily_member", fields: [related_id], references: [id])

  @@index([member_id])
  @@index([member_id, relation_type])
  @@index([related_id])
  @@index([relation_type])
  @@index([title])
}

model family_work {
  id            Int           @id @default(autoincrement())
  uuid          String        @unique
  member_id     Int
  company       String        @db.VarChar(100)
  position      String?       @db.VarChar(100)
  department    String?       @db.VarChar(100)
  start_date    DateTime      @db.Timestamptz(6)
  end_date      DateTime?     @db.Timestamptz(6)
  description   String?
  created_at    DateTime      @default(now()) @db.Timestamptz(6)
  updated_at    DateTime      @db.Timestamptz(6)
  is_deleted    Boolean       @default(false)
  family_member family_member @relation(fields: [member_id], references: [id])

  @@index([company])
  @@index([end_date])
  @@index([member_id])
  @@index([member_id, start_date])
  @@index([position])
  @@index([start_date])
}

model user_family {
  id          Int      @id @default(autoincrement())
  uuid        String   @unique
  user_id     String   @db.VarChar(255)
  family_id   Int
  role        String   @db.VarChar(50)
  permissions String?
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  updated_at  DateTime @db.Timestamptz(6)
  is_deleted  Boolean  @default(false)
  family      family   @relation(fields: [family_id], references: [id])
  users       User     @relation(fields: [user_id], references: [uuid])

  @@index([family_id])
  @@index([role])
  @@index([user_id, family_id])
  @@index([user_id])
}
