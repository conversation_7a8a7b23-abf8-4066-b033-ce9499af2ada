module.exports = {
  apps: [
    {
      name: 'youtube-analytics',
      script: 'npm',
      args: 'start',
      cwd: './',
      instances: 'max', // 使用所有 CPU 核心
      exec_mode: 'cluster',
      
      // 环境变量
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
      },
      
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        
        // Prisma 优化
        PRISMA_QUERY_ENGINE_LIBRARY: 'true',
        PRISMA_CLI_QUERY_ENGINE_TYPE: 'binary',
        
        // 性能优化
        UV_THREADPOOL_SIZE: '128', // 增加线程池大小
        NODE_OPTIONS: '--max-old-space-size=4096', // 增加内存限制
      },
      
      // 进程管理
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // 内存管理
      max_memory_restart: '1G',
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 监控
      monitoring: false, // 如果有 PM2 Plus，设置为 true
      
      // 健康检查
      health_check_http: {
        url: 'http://localhost:3000/api/health',
        interval: 30000, // 30 秒检查一次
        timeout: 5000,   // 5 秒超时
      },
      
      // 自动重启条件
      watch: false, // 生产环境不建议开启文件监控
      ignore_watch: [
        'node_modules',
        'logs',
        '.git',
        '.next',
        'public',
      ],
      
      // 进程间通信
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // 集群模式配置
      instance_var: 'INSTANCE_ID',
      
      // 自定义启动脚本（可选）
      // script: './server.js', // 如果有自定义服务器
    }
  ],
  
  // 部署配置（可选）
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/youtube-analytics.git',
      path: '/var/www/youtube-analytics',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    }
  }
};
