#!/bin/bash

# 生产环境部署脚本
# 用于优化数据库连接和性能配置

set -e

echo "🚀 开始生产环境部署..."

# 检查必要的环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "❌ 错误: DATABASE_URL 环境变量未设置"
    exit 1
fi

if [ -z "$AUTH_SECRET" ]; then
    echo "❌ 错误: AUTH_SECRET 环境变量未设置"
    exit 1
fi

# 设置生产环境变量
export NODE_ENV=production
export PRISMA_QUERY_ENGINE_LIBRARY=true
export PRISMA_CLI_QUERY_ENGINE_TYPE=binary

echo "📦 安装依赖..."
npm ci --only=production

echo "🗄️ 生成 Prisma 客户端..."
npx prisma generate

echo "🔄 运行数据库迁移..."
npx prisma migrate deploy

echo "🏗️ 构建应用..."
npm run build

echo "🔍 检查数据库连接..."
node -e "
const { prisma } = require('./dist/lib/prisma.js');
prisma.\$queryRaw\`SELECT 1 as test\`
  .then(() => {
    console.log('✅ 数据库连接成功');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  });
"

echo "🎯 优化数据库连接池..."
# 检查数据库 URL 是否包含连接池参数
if [[ $DATABASE_URL != *"connection_limit"* ]]; then
    echo "⚠️  警告: DATABASE_URL 未包含连接池优化参数"
    echo "建议添加以下参数:"
    echo "?connection_limit=20&pool_timeout=20&connect_timeout=10&statement_timeout=30000&idle_timeout=600"
fi

echo "📊 运行性能测试..."
# 简单的性能测试
node -e "
const { performance } = require('perf_hooks');
const { prisma } = require('./dist/lib/prisma.js');

async function performanceTest() {
  const tests = [
    { name: '简单查询', query: () => prisma.\$queryRaw\`SELECT 1\` },
    { name: '频道计数', query: () => prisma.ytbChannel.count() },
  ];

  for (const test of tests) {
    const start = performance.now();
    try {
      await test.query();
      const duration = performance.now() - start;
      console.log(\`✅ \${test.name}: \${duration.toFixed(2)}ms\`);
    } catch (error) {
      console.error(\`❌ \${test.name}: \${error.message}\`);
    }
  }
  
  await prisma.\$disconnect();
}

performanceTest().catch(console.error);
"

echo "🔧 设置进程管理..."
# 如果使用 PM2
if command -v pm2 &> /dev/null; then
    echo "使用 PM2 启动应用..."
    pm2 start ecosystem.config.js --env production
else
    echo "⚠️  建议安装 PM2 进行进程管理: npm install -g pm2"
fi

echo "✅ 生产环境部署完成!"
echo ""
echo "📋 部署信息:"
echo "- 环境: $NODE_ENV"
echo "- 数据库: $(echo $DATABASE_URL | sed 's/:[^@]*@/:***@/')"
echo "- 健康检查: curl http://localhost:3000/api/health"
echo ""
echo "🔍 监控建议:"
echo "- 使用 pm2 monit 监控进程"
echo "- 定期检查 /api/health 端点"
echo "- 监控数据库连接池使用情况"
echo "- 设置日志轮转和错误报警"
