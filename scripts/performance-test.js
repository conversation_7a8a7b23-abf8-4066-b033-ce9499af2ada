#!/usr/bin/env node

/**
 * 性能测试脚本
 * 用于测试数据库查询和 API 响应时间
 */

const { performance } = require('perf_hooks');
const path = require('path');

// 动态导入 Prisma（支持 TypeScript 和 JavaScript）
let prisma;
try {
  // 尝试从编译后的 dist 目录加载
  prisma = require(path.join(__dirname, '../dist/lib/prisma')).prisma;
} catch (error) {
  try {
    // 尝试从源码目录加载（需要 ts-node）
    require('ts-node/register');
    prisma = require(path.join(__dirname, '../src/lib/prisma')).prisma;
  } catch (tsError) {
    console.error('❌ 无法加载 Prisma 客户端');
    console.error('请确保已经构建项目 (npm run build) 或安装了 ts-node');
    process.exit(1);
  }
}

// 测试配置
const TEST_CONFIG = {
  warmupRounds: 3,
  testRounds: 10,
  concurrency: 5,
  testChannelId: 'UCXZCJLdBC09xxGZ6gcdrc6A',
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 性能测试函数
async function measurePerformance(name, fn, rounds = TEST_CONFIG.testRounds) {
  const times = [];
  
  // 预热
  colorLog('yellow', `🔥 预热 ${name}...`);
  for (let i = 0; i < TEST_CONFIG.warmupRounds; i++) {
    try {
      await fn();
    } catch (error) {
      colorLog('red', `预热失败: ${error.message}`);
    }
  }
  
  // 正式测试
  colorLog('blue', `🧪 测试 ${name} (${rounds} 轮)...`);
  for (let i = 0; i < rounds; i++) {
    const start = performance.now();
    try {
      await fn();
      const duration = performance.now() - start;
      times.push(duration);
    } catch (error) {
      colorLog('red', `测试失败 (第${i+1}轮): ${error.message}`);
      times.push(null);
    }
  }
  
  // 计算统计信息
  const validTimes = times.filter(t => t !== null);
  if (validTimes.length === 0) {
    colorLog('red', `❌ ${name}: 所有测试都失败了`);
    return null;
  }
  
  const avg = validTimes.reduce((a, b) => a + b, 0) / validTimes.length;
  const min = Math.min(...validTimes);
  const max = Math.max(...validTimes);
  const sorted = validTimes.sort((a, b) => a - b);
  const p95 = sorted[Math.floor(sorted.length * 0.95)];
  const p99 = sorted[Math.floor(sorted.length * 0.99)];
  const successRate = (validTimes.length / rounds) * 100;
  
  const stats = {
    name,
    rounds,
    successRate,
    avg: avg.toFixed(2),
    min: min.toFixed(2),
    max: max.toFixed(2),
    p95: p95.toFixed(2),
    p99: p99.toFixed(2),
  };
  
  // 输出结果
  const color = avg < 100 ? 'green' : avg < 500 ? 'yellow' : 'red';
  colorLog(color, `✅ ${name}:`);
  console.log(`   成功率: ${successRate.toFixed(1)}%`);
  console.log(`   平均: ${stats.avg}ms`);
  console.log(`   最小: ${stats.min}ms`);
  console.log(`   最大: ${stats.max}ms`);
  console.log(`   P95: ${stats.p95}ms`);
  console.log(`   P99: ${stats.p99}ms`);
  console.log('');
  
  return stats;
}

// 并发测试
async function concurrentTest(name, fn, concurrency = TEST_CONFIG.concurrency) {
  colorLog('magenta', `🚀 并发测试 ${name} (${concurrency} 并发)...`);
  
  const start = performance.now();
  const promises = Array(concurrency).fill().map(() => fn());
  
  try {
    await Promise.all(promises);
    const duration = performance.now() - start;
    const throughput = (concurrency / duration * 1000).toFixed(2);
    
    colorLog('green', `✅ ${name} 并发测试:`);
    console.log(`   总时间: ${duration.toFixed(2)}ms`);
    console.log(`   吞吐量: ${throughput} req/s`);
    console.log('');
    
    return { duration, throughput };
  } catch (error) {
    colorLog('red', `❌ ${name} 并发测试失败: ${error.message}`);
    return null;
  }
}

// 数据库测试
async function runDatabaseTests() {
  colorLog('cyan', '📊 开始数据库性能测试...');
  
  const tests = [
    {
      name: '简单查询',
      fn: () => prisma.$queryRaw`SELECT 1 as test`,
    },
    {
      name: '频道查询',
      fn: () => prisma.ytbChannel.findUnique({
        where: { channelId: TEST_CONFIG.testChannelId },
        select: {
          subscriberCount: true,
          viewCount: true,
          publishedAt: true,
          country: true,
          topicCategories: true,
          description: true,
        },
      }),
    },
    {
      name: '频道统计查询',
      fn: () => prisma.ytb_channel_time_statistics.findMany({
        where: {
          channel_id: TEST_CONFIG.testChannelId,
          period_type: 'day',
          deleted_at: null,
        },
        select: {
          view_change: true,
          subscriber_change: true,
          period_end: true,
        },
        orderBy: { period_end: 'desc' },
        take: 7,
      }),
    },
    {
      name: '频道计数',
      fn: () => prisma.ytbChannel.count(),
    },
  ];
  
  const results = [];
  for (const test of tests) {
    const result = await measurePerformance(test.name, test.fn);
    if (result) results.push(result);
    
    // 并发测试
    await concurrentTest(test.name, test.fn);
  }
  
  return results;
}

// HTTP API 测试
async function runApiTests() {
  colorLog('cyan', '🌐 开始 API 性能测试...');
  
  const baseUrl = process.env.TEST_URL || 'http://localhost:3000';
  
  const tests = [
    {
      name: 'Health Check',
      url: `${baseUrl}/api/health`,
    },
    {
      name: 'Channel About',
      url: `${baseUrl}/api/channels/${TEST_CONFIG.testChannelId}/about`,
    },
    {
      name: 'Channel Stats',
      url: `${baseUrl}/api/channels/${TEST_CONFIG.testChannelId}/stats?timeRange=7d`,
    },
    {
      name: 'Channel Charts',
      url: `${baseUrl}/api/channels/${TEST_CONFIG.testChannelId}/charts?timeRange=7d`,
    },
  ];
  
  const results = [];
  for (const test of tests) {
    const result = await measurePerformance(test.name, async () => {
      const response = await fetch(test.url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    });
    
    if (result) results.push(result);
    
    // 并发测试
    await concurrentTest(test.name, async () => {
      const response = await fetch(test.url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      return response.json();
    });
  }
  
  return results;
}

// 生成报告
function generateReport(dbResults, apiResults) {
  colorLog('cyan', '📋 性能测试报告');
  console.log('='.repeat(50));
  
  console.log('\n📊 数据库测试结果:');
  console.table(dbResults);
  
  console.log('\n🌐 API 测试结果:');
  console.table(apiResults);
  
  // 性能评级
  const allResults = [...dbResults, ...apiResults];
  const avgPerformance = allResults.reduce((sum, r) => sum + parseFloat(r.avg), 0) / allResults.length;
  
  let grade, color;
  if (avgPerformance < 50) {
    grade = 'A+';
    color = 'green';
  } else if (avgPerformance < 100) {
    grade = 'A';
    color = 'green';
  } else if (avgPerformance < 200) {
    grade = 'B';
    color = 'yellow';
  } else if (avgPerformance < 500) {
    grade = 'C';
    color = 'yellow';
  } else {
    grade = 'D';
    color = 'red';
  }
  
  colorLog(color, `\n🏆 总体性能评级: ${grade} (平均响应时间: ${avgPerformance.toFixed(2)}ms)`);
  
  // 建议
  console.log('\n💡 优化建议:');
  if (avgPerformance > 200) {
    console.log('- 考虑添加数据库索引');
    console.log('- 检查数据库连接池配置');
    console.log('- 考虑添加缓存层');
  }
  if (avgPerformance > 500) {
    console.log('- 检查数据库服务器性能');
    console.log('- 优化查询语句');
    console.log('- 考虑数据库分片');
  }
  if (avgPerformance < 100) {
    console.log('- 性能表现优秀！');
    console.log('- 可以考虑增加更多功能');
  }
}

// 主函数
async function main() {
  try {
    colorLog('green', '🚀 开始性能测试...');
    console.log(`测试配置: ${JSON.stringify(TEST_CONFIG, null, 2)}\n`);
    
    const dbResults = await runDatabaseTests();
    const apiResults = await runApiTests();
    
    generateReport(dbResults, apiResults);
    
    colorLog('green', '✅ 性能测试完成！');
  } catch (error) {
    colorLog('red', `❌ 测试失败: ${error.message}`);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { measurePerformance, concurrentTest };
