#!/usr/bin/env node

/**
 * 直接使用 googleapis 库测试 YouTube API
 * 参考官方文档实现
 */

const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');

// 加载环境变量
function loadEnvFile() {
  const envFiles = ['.env.local', '.env'];
  
  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
      console.log(`📄 加载环境文件: ${envFile}`);
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          const [key, ...valueParts] = trimmedLine.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').replace(/^['"]|['"]$/g, '');
            if (!process.env[key]) {
              process.env[key] = value;
            }
          }
        }
      }
      break;
    }
  }
}

loadEnvFile();

const API_KEY = process.env.YOUTUBE_API_KEY;

/**
 * 测试频道信息获取
 */
async function testChannelAPI() {
  console.log('\n🔍 测试频道 API (googleapis 库)...\n');
  
  if (!API_KEY) {
    console.log('⚠️  跳过测试 (API Key 未配置)');
    return;
  }

  // 初始化 YouTube API 客户端
  const youtube = google.youtube({
    version: 'v3',
    auth: API_KEY
  });

  const testCases = [
    { type: 'id', value: 'UCVHFbqXqoYvEWM1Ddxl0QDg', name: 'Google Developers (ID)' },
    { type: 'username', value: 'GoogleDevelopers', name: 'Google Developers (Username)' },
    { type: 'username', value: 'TechCrunch', name: 'TechCrunch' },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`📡 测试: ${testCase.name}`);
      const startTime = Date.now();

      // 构建请求参数
      const params = {
        part: ['snippet', 'statistics', 'contentDetails'],
        maxResults: 1
      };

      if (testCase.type === 'id') {
        params.id = [testCase.value];
      } else {
        params.forUsername = testCase.value;
      }

      console.log(`   📋 请求参数:`, JSON.stringify(params, null, 2));

      // 调用 API
      const response = await youtube.channels.list(params);
      const duration = Date.now() - startTime;

      console.log(`   ⏱️  请求耗时: ${duration}ms`);
      console.log(`   📊 响应状态: ${response.status}`);

      if (response.data.items && response.data.items.length > 0) {
        const channel = response.data.items[0];
        console.log(`   ✅ 成功: ${channel.snippet.title}`);
        console.log(`   📊 订阅者: ${formatNumber(parseInt(channel.statistics.subscriberCount))}`);
        console.log(`   👀 观看数: ${formatNumber(parseInt(channel.statistics.viewCount))}`);
        console.log(`   🎥 视频数: ${formatNumber(parseInt(channel.statistics.videoCount))}`);
        console.log(`   🆔 频道ID: ${channel.id}`);
        console.log(`   🔗 自定义URL: ${channel.snippet.customUrl || '无'}`);
      } else {
        console.log(`   ❌ 未找到频道数据`);
      }
    } catch (error) {
      console.log(`   💥 错误: ${error.message}`);
      if (error.response) {
        console.log(`   📄 响应状态: ${error.response.status}`);
        console.log(`   📄 响应数据:`, error.response.data);
      }
    }
    
    console.log('');
  }
}

/**
 * 测试视频信息获取
 */
async function testVideoAPI() {
  console.log('\n🎬 测试视频 API (googleapis 库)...\n');
  
  if (!API_KEY) {
    console.log('⚠️  跳过测试 (API Key 未配置)');
    return;
  }

  // 初始化 YouTube API 客户端
  const youtube = google.youtube({
    version: 'v3',
    auth: API_KEY
  });

  const testCases = [
    { id: 'dQw4w9WgXcQ', name: 'Rick Astley - Never Gonna Give You Up' },
    { id: 'jNQXAC9IVRw', name: 'Me at the zoo (First YouTube video)' },
    { id: '9bZkp7q19f0', name: 'PSY - GANGNAM STYLE' },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`📡 测试: ${testCase.name}`);
      const startTime = Date.now();

      // 构建请求参数
      const params = {
        part: ['snippet', 'statistics', 'contentDetails'],
        id: [testCase.id],
        maxResults: 1
      };

      console.log(`   📋 请求参数:`, JSON.stringify(params, null, 2));

      // 调用 API
      const response = await youtube.videos.list(params);
      const duration = Date.now() - startTime;

      console.log(`   ⏱️  请求耗时: ${duration}ms`);
      console.log(`   📊 响应状态: ${response.status}`);

      if (response.data.items && response.data.items.length > 0) {
        const video = response.data.items[0];
        console.log(`   ✅ 成功: ${video.snippet.title}`);
        console.log(`   📺 频道: ${video.snippet.channelTitle}`);
        console.log(`   👀 观看数: ${formatNumber(parseInt(video.statistics.viewCount))}`);
        console.log(`   👍 点赞数: ${formatNumber(parseInt(video.statistics.likeCount))}`);
        console.log(`   💬 评论数: ${formatNumber(parseInt(video.statistics.commentCount))}`);
        console.log(`   ⏱️  时长: ${formatDuration(video.contentDetails.duration)}`);
        console.log(`   📅 发布: ${new Date(video.snippet.publishedAt).toLocaleDateString()}`);
      } else {
        console.log(`   ❌ 未找到视频数据`);
      }
    } catch (error) {
      console.log(`   💥 错误: ${error.message}`);
      if (error.response) {
        console.log(`   📄 响应状态: ${error.response.status}`);
        console.log(`   📄 响应数据:`, error.response.data);
      }
    }
    
    console.log('');
  }
}

/**
 * 测试搜索功能
 */
async function testSearchAPI() {
  console.log('\n🔍 测试搜索 API (googleapis 库)...\n');
  
  if (!API_KEY) {
    console.log('⚠️  跳过测试 (API Key 未配置)');
    return;
  }

  // 初始化 YouTube API 客户端
  const youtube = google.youtube({
    version: 'v3',
    auth: API_KEY
  });

  try {
    console.log(`📡 测试搜索: "Google Developers"`);
    const startTime = Date.now();

    // 搜索频道
    const response = await youtube.search.list({
      part: ['snippet'],
      q: 'Google Developers',
      type: ['channel'],
      maxResults: 5
    });

    const duration = Date.now() - startTime;
    console.log(`   ⏱️  请求耗时: ${duration}ms`);
    console.log(`   📊 响应状态: ${response.status}`);

    if (response.data.items && response.data.items.length > 0) {
      console.log(`   ✅ 找到 ${response.data.items.length} 个结果:`);
      response.data.items.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.snippet.title}`);
        console.log(`      频道ID: ${item.snippet.channelId}`);
        console.log(`      描述: ${item.snippet.description.substring(0, 100)}...`);
      });
    } else {
      console.log(`   ❌ 未找到搜索结果`);
    }
  } catch (error) {
    console.log(`   💥 错误: ${error.message}`);
    if (error.response) {
      console.log(`   📄 响应状态: ${error.response.status}`);
      console.log(`   📄 响应数据:`, error.response.data);
    }
  }
}

/**
 * 格式化数字显示
 */
function formatNumber(num) {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * 格式化 ISO 8601 持续时间
 */
function formatDuration(duration) {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return '0:00';

  const hours = parseInt(match[1] || '0');
  const minutes = parseInt(match[2] || '0');
  const seconds = parseInt(match[3] || '0');

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 YouTube API 直接测试 (googleapis 库)\n');
  console.log('=' .repeat(50));
  
  console.log(`🔑 API Key: ${API_KEY ? '已配置' : '未配置'}`);
  if (API_KEY) {
    console.log(`   长度: ${API_KEY.length} 字符`);
    console.log(`   前缀: ${API_KEY.substring(0, 10)}...`);
  }

  try {
    await testChannelAPI();
    await testVideoAPI();
    await testSearchAPI();
    
    console.log('\n✨ 测试完成!');
    console.log('\n💡 提示:');
    console.log('   - 如果测试成功，说明 googleapis 库可以正常工作');
    console.log('   - 如果测试失败，请检查网络连接和 API Key 配置');
    console.log('   - 可以将此实现集成到 Next.js API 路由中');
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, testChannelAPI, testVideoAPI, testSearchAPI };
